{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": false, "tsx": true, "tailwind": {"config": "", "css": "packages/desktop/src/renderer/src/index.css", "baseColor": "neutral", "cssVariables": true, "prefix": ""}, "aliases": {"components": "@clarity/shadcn/components", "utils": "@clarity/shadcn/lib/utils", "ui": "@clarity/shadcn/ui", "lib": "@clarity/shadcn/lib", "hooks": "@clarity/shadcn/hooks"}, "iconLibrary": "lucide"}