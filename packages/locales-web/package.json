{"name": "locales-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/devtools": "latest", "@pinia/nuxt": "^0.11.2", "nuxt": "^4.0.2", "pinia": "^3.0.3", "vue": "latest", "vue-router": "latest"}, "devDependencies": {"@nuxt/typescript-build": "^3.0.2", "@unocss/nuxt": "66.3.3", "@vueuse/nuxt": "13.5.0"}}