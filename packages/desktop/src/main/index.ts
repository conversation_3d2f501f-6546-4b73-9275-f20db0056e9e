import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/<EMAIL>?asset'
import { runMigrate } from './db'
import { RouterType } from '@egoist/tipc/main'
import { router } from './tipc'
import { WindowManager } from './managers/window.manager'
import consola from 'consola'

export const registerIpcMainWithLogger = (router: RouterType) => {
  for (const [name, route] of Object.entries(router)) {
    ipcMain.handle(name, (e, payload) => {
      consola.withTag(name).info(`Renderer Invoke -> Main Handle ${name}`)
      return route.action({ context: { sender: e.sender }, input: payload })
    })
    consola.withTag('TIPC').success(`Registered IPC Main Handler: ${name}`)
  }
}

async function createWindow(): Promise<void> {
  // 创建窗口管理器实例
  const windowManager = new WindowManager()

  // 获取窗口配置选项
  const windowOptions = await windowManager.getWindowOptions()

  // Create the browser window.
  const mainWindow = new BrowserWindow({
    ...windowOptions,
    show: false,
    titleBarStyle: 'hiddenInset',
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  // 设置窗口事件监听器
  windowManager.setupWindowListeners(mainWindow)

  mainWindow.on('ready-to-show', async () => {
    mainWindow.show()
    // 恢复窗口状态（如最大化状态）
    await windowManager.restoreWindowState()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  // 注册 TIPC 路由器
  registerIpcMainWithLogger(router)

  await runMigrate()
  await createWindow()

  app.on('activate', async function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) await createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
