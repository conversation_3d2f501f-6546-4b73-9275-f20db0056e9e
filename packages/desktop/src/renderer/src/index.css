@import "../../../../shadcn/global.css";
@plugin './lib/utils/heroui.ts';
@source '../../../../../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}';

@custom-variant dark (&:is(.dark *));

* {
  user-select: none;
}

body {
  -webkit-font-smoothing: antialiased !important;
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.24 0 0);
  /* 主背景 - 深灰 */
  --foreground: oklch(0.96 0 0);
  /* 主文字 - 高亮白 */
  --card: oklch(0.26 0 0);
  /* 卡片背景 - 略亮 */
  --card-foreground: oklch(0.96 0 0);
  --popover: oklch(0.26 0 0);
  --popover-foreground: oklch(0.96 0 0);
  --primary: oklch(0.96 0 0);
  /* 高亮按钮等 */
  --primary-foreground: oklch(0.24 0 0);
  --secondary: oklch(0.35 0 0);
  /* 次级区域背景 */
  --secondary-foreground: oklch(0.96 0 0);
  --muted: oklch(0.35 0 0);
  --muted-foreground: oklch(0.7 0 0);
  --accent: oklch(0.35 0 0);
  --accent-foreground: oklch(0.96 0 0);
  --destructive: oklch(0.5 0.14 25);
  /* 保持原有饱和红 */
  --destructive-foreground: oklch(0.9 0.15 25);
  --border: oklch(0.3 0 0);
  --input: oklch(0.3 0 0);
  --ring: oklch(0.5 0 0);

  --chart-1: oklch(0.55 0.22 264);
  --chart-2: oklch(0.65 0.17 162);
  --chart-3: oklch(0.7 0.18 70);
  --chart-4: oklch(0.6 0.25 304);
  --chart-5: oklch(0.6 0.24 16);

  --sidebar: oklch(0.26 0 0);
  --sidebar-foreground: oklch(0.96 0 0);
  --sidebar-primary: oklch(0.55 0.22 264);
  --sidebar-primary-foreground: oklch(0.96 0 0);
  --sidebar-accent: oklch(0.35 0 0);
  --sidebar-accent-foreground: oklch(0.96 0 0);
  --sidebar-border: oklch(0.3 0 0);
  --sidebar-ring: oklch(0.5 0 0);
}