/* 现代 macOS 风格全局文件拖拽样式 */

body.file-dragging {
  position: relative;
}

/* 背景遮罩层 - 更轻的遮罩效果 */
body.file-dragging::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.75);
  backdrop-filter: blur(8px) saturate(1.2);
  z-index: 9998;
  pointer-events: none;
  animation: overlayFadeIn 0.2s ease-out;
}

/* 拖拽区域 - 大型虚线边框区域 */
body.file-dragging::after {
  content: '';
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: min(480px, 80vw);
  height: min(320px, 60vh);
  z-index: 9999;
  pointer-events: none;
  border: 2px dashed #007AFF;
  border-radius: 16px;
  background: rgba(0, 122, 255, 0.03);
  animation: dropZoneIn 0.25s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 图标和文字容器 */
.file-drop-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #007AFF;
}

/* SVG 图标样式 */
.file-drop-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #007AFF;
}

/* 文字样式 */
.file-drop-text {
  color: #1d1d1f;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: -0.01em;
  text-align: center;
  line-height: 1.4;
  margin-bottom: 4px;
}

.file-drop-subtitle {
  color: #86868b;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  line-height: 1.3;
}

/* 深色模式适配 */
.dark body.file-dragging::before {
  background: rgba(0, 0, 0, 0.6);
}

.dark body.file-dragging::after {
  border-color: #0A84FF;
  background: rgba(10, 132, 255, 0.05);
}

.dark .file-drop-overlay {
  color: #0A84FF;
}

.dark .file-drop-icon {
  color: #0A84FF;
}

.dark .file-drop-text {
  color: #f5f5f7;
}

.dark .file-drop-subtitle {
  color: #86868b;
}

/* 动画定义 */
@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) saturate(1);
  }

  to {
    opacity: 1;
    backdrop-filter: blur(8px) saturate(1.2);
  }
}

@keyframes dropZoneIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
    border-color: transparent;
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    border-color: #007AFF;
  }
}



/* 禁用文本选择 */
body.file-dragging * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}