/**
 * Command Palette 详情视图通用组件
 *
 * 这些组件专门用于插件在 CommandView 中渲染详情内容
 * 所有组件都遵循 command-palette 的设计语言
 */

// 基础组件
export { DetailItem } from './DetailItem'
export { DetailSection } from './DetailSection'
export { DetailInput } from './DetailInput'
export { DetailButton } from './DetailButton'

// 状态组件
export { DetailStatus } from './DetailStatus'
export { DetailLoading } from './DetailLoading'
export { DetailEmpty } from './DetailEmpty'

// 布局组件
export { DetailLayout, DetailSidebar, DetailMain } from './DetailLayout'
