/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as SettingsRouteImport } from './routes/settings'
import { Route as FilesRouteImport } from './routes/files'
import { Route as ExpensesRouteImport } from './routes/expenses'
import { Route as ErrorTestRouteImport } from './routes/error-test'
import { Route as CompetitionsRouteImport } from './routes/competitions'
import { Route as AboutRouteImport } from './routes/about'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ProjectsIndexRouteImport } from './routes/projects/index'
import { Route as ProjectsProjectIdRouteImport } from './routes/projects/$projectId'

const SettingsRoute = SettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRouteImport,
} as any)
const FilesRoute = FilesRouteImport.update({
  id: '/files',
  path: '/files',
  getParentRoute: () => rootRouteImport,
} as any)
const ExpensesRoute = ExpensesRouteImport.update({
  id: '/expenses',
  path: '/expenses',
  getParentRoute: () => rootRouteImport,
} as any)
const ErrorTestRoute = ErrorTestRouteImport.update({
  id: '/error-test',
  path: '/error-test',
  getParentRoute: () => rootRouteImport,
} as any)
const CompetitionsRoute = CompetitionsRouteImport.update({
  id: '/competitions',
  path: '/competitions',
  getParentRoute: () => rootRouteImport,
} as any)
const AboutRoute = AboutRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ProjectsIndexRoute = ProjectsIndexRouteImport.update({
  id: '/projects/',
  path: '/projects/',
  getParentRoute: () => rootRouteImport,
} as any)
const ProjectsProjectIdRoute = ProjectsProjectIdRouteImport.update({
  id: '/projects/$projectId',
  path: '/projects/$projectId',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/competitions': typeof CompetitionsRoute
  '/error-test': typeof ErrorTestRoute
  '/expenses': typeof ExpensesRoute
  '/files': typeof FilesRoute
  '/settings': typeof SettingsRoute
  '/projects/$projectId': typeof ProjectsProjectIdRoute
  '/projects': typeof ProjectsIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/competitions': typeof CompetitionsRoute
  '/error-test': typeof ErrorTestRoute
  '/expenses': typeof ExpensesRoute
  '/files': typeof FilesRoute
  '/settings': typeof SettingsRoute
  '/projects/$projectId': typeof ProjectsProjectIdRoute
  '/projects': typeof ProjectsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/competitions': typeof CompetitionsRoute
  '/error-test': typeof ErrorTestRoute
  '/expenses': typeof ExpensesRoute
  '/files': typeof FilesRoute
  '/settings': typeof SettingsRoute
  '/projects/$projectId': typeof ProjectsProjectIdRoute
  '/projects/': typeof ProjectsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about'
    | '/competitions'
    | '/error-test'
    | '/expenses'
    | '/files'
    | '/settings'
    | '/projects/$projectId'
    | '/projects'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/about'
    | '/competitions'
    | '/error-test'
    | '/expenses'
    | '/files'
    | '/settings'
    | '/projects/$projectId'
    | '/projects'
  id:
    | '__root__'
    | '/'
    | '/about'
    | '/competitions'
    | '/error-test'
    | '/expenses'
    | '/files'
    | '/settings'
    | '/projects/$projectId'
    | '/projects/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutRoute: typeof AboutRoute
  CompetitionsRoute: typeof CompetitionsRoute
  ErrorTestRoute: typeof ErrorTestRoute
  ExpensesRoute: typeof ExpensesRoute
  FilesRoute: typeof FilesRoute
  SettingsRoute: typeof SettingsRoute
  ProjectsProjectIdRoute: typeof ProjectsProjectIdRoute
  ProjectsIndexRoute: typeof ProjectsIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/files': {
      id: '/files'
      path: '/files'
      fullPath: '/files'
      preLoaderRoute: typeof FilesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/expenses': {
      id: '/expenses'
      path: '/expenses'
      fullPath: '/expenses'
      preLoaderRoute: typeof ExpensesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/error-test': {
      id: '/error-test'
      path: '/error-test'
      fullPath: '/error-test'
      preLoaderRoute: typeof ErrorTestRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/competitions': {
      id: '/competitions'
      path: '/competitions'
      fullPath: '/competitions'
      preLoaderRoute: typeof CompetitionsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/projects/': {
      id: '/projects/'
      path: '/projects'
      fullPath: '/projects'
      preLoaderRoute: typeof ProjectsIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/projects/$projectId': {
      id: '/projects/$projectId'
      path: '/projects/$projectId'
      fullPath: '/projects/$projectId'
      preLoaderRoute: typeof ProjectsProjectIdRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutRoute: AboutRoute,
  CompetitionsRoute: CompetitionsRoute,
  ErrorTestRoute: ErrorTestRoute,
  ExpensesRoute: ExpensesRoute,
  FilesRoute: FilesRoute,
  SettingsRoute: SettingsRoute,
  ProjectsProjectIdRoute: ProjectsProjectIdRoute,
  ProjectsIndexRoute: ProjectsIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
