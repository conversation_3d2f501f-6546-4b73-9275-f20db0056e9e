import { create } from 'zustand'
import { User, UserState, UserActions } from '@renderer/types/user'
import { tipcClient } from '@renderer/lib/tipc-client'

type AppStore = UserState &
  UserActions & {
    // 新增异步操作
    loadUser: () => Promise<void>
    updateUserAsync: (partialUser: Partial<User>) => Promise<void>
    initializeUser: () => Promise<void>
  }

export const useAppStore = create<AppStore>((set, get) => ({
  // 初始状态
  user: null,
  isLoading: false,
  error: null,

  // 同步Actions（保持向后兼容）
  setUser: (user: User) => set({ user, error: null }),

  updateUser: (partialUser: Partial<User>) =>
    set((state) => ({
      user: state.user ? { ...state.user, ...partialUser } : null,
      error: null
    })),

  clearUser: () => set({ user: null, error: null }),

  setError: (error: string | null) => set({ error }),

  setLoading: (isLoading: boolean) => set({ isLoading }),

  // 新增异步Actions
  loadUser: async () => {
    set({ isLoading: true, error: null })
    try {
      const user = await tipcClient.getCurrentUser()
      set({ user, isLoading: false })
    } catch (error) {
      console.error('加载用户信息失败:', error)
      set({
        error: error instanceof Error ? error.message : '加载用户信息失败',
        isLoading: false
      })
    }
  },

  updateUserAsync: async (partialUser: Partial<User>) => {
    const currentUser = get().user
    if (!currentUser?.id) {
      set({ error: '用户信息不存在' })
      return
    }

    set({ isLoading: true, error: null })
    try {
      const updatedUser = await tipcClient.updateUser({
        id: currentUser.id,
        ...partialUser
      })
      set({ user: updatedUser, isLoading: false })
    } catch (error) {
      console.error('更新用户信息失败:', error)
      set({
        error: error instanceof Error ? error.message : '更新用户信息失败',
        isLoading: false
      })
    }
  },

  initializeUser: async () => {
    set({ isLoading: true, error: null })
    try {
      // 首先检查是否已有用户
      const existingUser = await tipcClient.getCurrentUser()
      if (existingUser) {
        set({ user: existingUser, isLoading: false })
        return
      }

      // 如果没有用户，初始化默认用户
      const defaultUser = await tipcClient.initializeDefaultUser()
      set({ user: defaultUser, isLoading: false })
    } catch (error) {
      console.error('初始化用户失败:', error)
      set({
        error: error instanceof Error ? error.message : '初始化用户失败',
        isLoading: false
      })
    }
  }
}))
