import { PropsWithChildren, useEffect } from 'react'
import { useAppStore } from '@renderer/stores/app'

export function DataProvider({ children }: PropsWithChildren) {
  const setUser = useAppStore((state) => state.setUser)

  useEffect(() => {
    setUser({
      name: 'Wib<PERSON> <PERSON>',
      email: '<EMAIL>',
      avatar: 'https://github.com/wibus-wee.png',
      role: 'founder'
    })
  }, [setUser])

  return <>{children}</>
}
