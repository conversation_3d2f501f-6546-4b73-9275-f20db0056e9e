import { PropsWithChildren, useEffect } from 'react'
import { useAppStore } from '@renderer/stores/app'

export function DataProvider({ children }: PropsWithChildren) {
  const initializeUser = useAppStore((state) => state.initializeUser)
  const isLoading = useAppStore((state) => state.isLoading)
  const error = useAppStore((state) => state.error)

  useEffect(() => {
    // 从数据库加载用户信息，如果不存在则创建默认用户
    initializeUser()
  }, [initializeUser])

  // 可以在这里添加加载状态或错误处理的UI
  if (isLoading) {
    // 这里可以显示加载指示器，但为了不影响现有UI，暂时不显示
    console.log('正在加载用户信息...')
  }

  if (error) {
    console.error('用户信息加载错误:', error)
    // 这里可以显示错误信息，但为了不影响现有UI，暂时只记录日志
  }

  return <>{children}</>
}
