{"name": "ClarityFile", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": {"email": "<EMAIL>", "name": "wibus-wee", "url": "https://wibus.ren"}, "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "i18n:types": "tsx scripts/generate-i18n-types.ts", "start": "pnpm rebuild:sqlite3:electron && electron-vite preview", "dev": "pnpm rebuild:sqlite3:electron && electron-vite dev", "build": "npm run typecheck && pnpm rebuild:sqlite3:electron && electron-vite build", "electron:mac": "npm run build && electron-builder --mac --config", "build:unpack": "npm run build", "rebuild:sqlite3:electron": "electron-rebuild -f -w better-sqlite3", "rebuild:sqlite3": "pnpm rebuild better-sqlite3", "drizzle-kit": "pnpm rebuild:sqlite3 && npx drizzle-kit", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@clarity/shadcn": "workspace:*", "@egoist/tipc": "^0.3.2", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@heroui/react": "2.8.0-beta.5", "@hookform/resolvers": "^5.2.1", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.130.9", "@tanstack/react-router-devtools": "^1.130.9", "@tanstack/react-virtual": "^3.13.12", "better-sqlite3": "^12.2.0", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.4", "electron-updater": "^6.6.2", "fuse.js": "7.1.0", "i18next": "25.3.2", "i18next-browser-languagedetector": "8.2.0", "immer": "10.1.1", "mime-types": "^3.0.1", "nuqs": "^2.4.3", "pinyin-pro": "3.26.0", "react-compiler-runtime": "19.1.0-rc.2", "react-hook-form": "^7.61.1", "react-i18next": "15.6.0", "react-scan": "^0.4.3", "sonner": "^2.0.6", "swr": "^2.3.4", "use-debounce": "10.0.5", "vaul": "^1.1.2", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@electron-forge/cli": "^7.8.2", "@electron-forge/maker-deb": "^7.8.2", "@electron-forge/maker-dmg": "^7.8.2", "@electron-forge/maker-rpm": "^7.8.2", "@electron-forge/maker-squirrel": "^7.8.2", "@electron-forge/maker-zip": "^7.8.2", "@electron-forge/shared-types": "^7.8.2", "@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.1.0", "@electron-toolkit/tsconfig": "^1.0.1", "@electron/fuses": "^1.8.0", "@tanstack/router-plugin": "^1.130.9", "@types/mime-types": "^3.0.1", "babel-plugin-react-compiler": "19.1.0-rc.2", "c8": "^10.1.3", "drizzle-kit": "^0.31.4", "electron": "^37.2.5", "electron-builder": "26.0.12", "electron-vite": "^4.0.0", "es-toolkit": "1.39.7", "i18next-resources-to-backend": "1.2.1", "prompts": "2.4.2", "table": "6.9.0", "tsx": "4.20.3", "vite": "7.0.4"}}