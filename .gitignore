node_modules
dist
out
.DS_Store
.eslintcache
*.log*
*.db
drizzle

!default.mode1v3
!default.mode2v3
!default.pbxuser
!default.perspectivev3
!default.xcworkspace
*.app
*.app/
*.dSYM
*.dSYM.zip
*.hmap
*.ipa
*.lcov
*.lock
*.log
*.mode1v3
*.mode2v3
*.moved-aside
*.pbxuser
*.perspectivev3
*.pid
*.pid.lock
*.seed
*.swp
*.tgz
*.tsbuildinfo
*.xccheckout
*.xcscmblueprint
*.xcuserstate
*~.nib
.AppleDB
.AppleDesktop
.AppleDouble
.DS_Store
.DocumentRevisions-V100
.LSOverride
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
._*
.apdisk
.build
.bundle
.cache
.cache/
.com.apple.timemachine.donotpresent
.dynamodb/
.env
.env.test
.eslintcache
.fseventsd
.fusebox/
.grunt
.idea
.lock-wscript
.next
.node_repl_history
.npm
.nuxt
.nyc_output
.parcel-cache
.pnp.*
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.serverless/
.swiftpm
.tern-port
.vscode-test
.vuepress/dist
.yarn-integrity
.yarn/build-state.yml
.yarn/cache
.yarn/unplugged
/*.gcno
Artifacts
Artifacts/
CI
CI-Pods.tar
Carthage/Build
Carthage/Build/
DerivedData
DerivedData/
Icon
Network Trash Folder
Pipeline/Dockers/Buildtime/
Podfile.lock
Pods/
Temporary Items
artifacts
artifacts/
bower_components
build/Release
coverage
default.profraw
dist
dockerbuild
dockermnt
fastlane/Preview.html
fastlane/report.xml
fastlane/screenshots/**/*.png
fastlane/test_output
iOSInjectionProject/
jspm_packages/
lerna-debug.log*
lib-cov
logs
node_modules/
npm-debug.log*
pids
profile
project.xcworkspace
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
temp/
temps/
web_modules/
xcuserdata
xcuserdata/
yarn-debug.log*
yarn-error.log*
**/*.bun
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel
**/*.trace
**/*.zip
**/*.tar.gz
**/*.tgz
**/*.log
package-lock.json
**/*.bun-build
.output