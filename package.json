{"name": "ClarityFile", "version": "1.0.0", "description": "", "scripts": {"desktop": "pnpm -C packages/desktop", "www": "pnpm -C packages/www", "locales": "pnpm -C packages/locales-web", "i18n:stats": "tsx scripts/calculate-i18n-completeness.ts", "i18n:add-locale": "tsx scripts/add-i18n-locale.ts", "i18n:validate": "tsx scripts/validate-i18n-keys.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "destr": "2.0.5", "framer-motion": "^12.23.12", "lucide-react": "^0.535.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6"}, "devDependencies": {"@types/node": "^22.17.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "@vitest/ui": "^3.2.4", "consola": "^3.4.2", "eslint": "^9.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "6.0.0-rc1", "eslint-plugin-react-refresh": "^0.4.20", "prettier": "^3.6.2", "react": "19.1.0", "react-dom": "19.1.0", "typescript": "^5.8.3", "vite": "7.0.4", "vitest": "^3.2.4"}, "pnpm": {"onlyBuiltDependencies": ["@heroui/shared-utils", "@tailwindcss/oxide", "better-sqlite3", "electron", "esbuild", "fs-xattr", "macos-alias"]}}