name: Test Desktop App

on:
  push:
    branches: [main]
    paths:
      - 'packages/desktop/**'
      - 'packages/shadcn/**'
      - 'locales/**'
      - '.github/workflows/test-desktop.yml'
      - 'package.json'
      - 'pnpm-lock.yaml'
  pull_request:
    branches: [main]
    paths:
      - 'packages/desktop/**'
      - 'packages/shadcn/**'
      - 'locales/**'
      - '.github/workflows/test-desktop.yml'
      - 'package.json'
      - 'pnpm-lock.yaml'
  workflow_dispatch:

# Cancel in-progress runs for the same workflow and branch
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Lint desktop app
        run: pnpm desktop lint

      - name: Type check desktop app
        run: pnpm desktop typecheck

  build-matrix:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [macos-latest]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build desktop app
        run: pnpm desktop build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: desktop-build-${{ matrix.os }}
          path: packages/desktop/out/
          retention-days: 7
