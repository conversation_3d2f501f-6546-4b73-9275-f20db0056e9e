# ClarityFile Product Overview

ClarityFile (明档 Míng <PERSON>) is a localized intelligent document version and transaction association center designed specifically for academic teams and multi-project, multi-competition participants.

## Core Purpose
- **Document Version Management**: Track and manage multiple versions of PPTs, business plans, project descriptions across different competitions and requirements
- **Project-Centric Organization**: All information and files are organized around projects as the core unit
- **Metadata-Driven**: Rich metadata (project, competition, stage, version tags, types) precisely describes each file and information entry
- **Local-First**: Data stored locally with user control, compatible with existing cloud sync solutions

## Key Features
- Separation of "logical documents" from "physical versions"
- Automatic intelligent naming and structured local storage
- Strong information association breaking down silos
- File physical layer and business logic layer separation
- Unified registry for all managed physical files

## Target Users
Academic teams, researchers, and professionals managing multiple projects and competitions with complex document versioning needs.

## Current Status
🧪 Work in Progress - Desktop app for macOS/Windows, with PWA coming soon.