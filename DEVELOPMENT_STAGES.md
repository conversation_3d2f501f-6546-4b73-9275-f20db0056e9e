## 渐进式开发

### Phase 0: 最小可行骨架 (Schema 核心中的核心)

- **目标：** 搭建应用框架，实现最最基础的文件“注册”能力，验证文件系统操作。
- **激活的Schema部分：**
  - `managed_files`: `id`, `physical_path`, `original_name`, `created_at` (最简版)
  - `projects`: `id`, `name`, `created_at` (最简版)
- **核心功能实现：**
  1.  **[设计与编码]** 应用基本框架，主窗口。
  2.  **[设计与编码]** 设置模块：用户指定 `CLARITY_FILE_ROOT`。
  3.  **[设计与编码]** 项目创建：能在UI上创建一个项目，并在 `projects` 表存入记录。
  4.  **[设计与编码]** **“裸”文件导入功能（MVP的MVP）：**
      - 用户选择一个文件。
      - 应用将该文件**复制**到一个由项目名构成的简单子文件夹下（例如 `CLARITY_FILE_ROOT/Projects/[ProjectName]/[OriginalFileName]`），文件名暂时不做复杂处理。
      - 在 `managed_files` 表中记录这个文件的 `physical_path` 和 `original_name`。
      - (可选) 在 `projects` 表旁边（或一个极简的临时关联表）记录这个文件属于哪个项目。
- **产出：** 一个能创建项目，并把文件“扔”到项目名下的简单文件夹里，同时数据库里有这个文件的记录。**文件夹结构极简，命名也极简。**
- **价值：** 验证了Electron框架、数据库连接、最基本的文件操作。

### Phase 1: 核心文档版本管理与智能命名雏形 (Schema 关键部分激活)

- **目标：** 实现文档版本概念，引入初步的智能命名和按类型存放。
- **激活的Schema部分（在Phase 0基础上）：**
  - `logical_documents`: `id`, `project_id`, `name`, `type`, `created_at`
  - `document_versions`: `id`, `logical_document_id`, `managed_file_id`, `version_tag`, `created_at`
  - `managed_files`: 补充 `file_size_bytes`, `file_hash` (如果开始做查重或校验)。
- **核心功能实现：**
  1.  **[设计与编码]** 逻辑文档创建：在项目下创建逻辑文档（如“商业计划书”），定义类型。
  2.  **[设计与编码]** **智能命名与存放 V1：**
      - 设计第一版简单的文件名生成规则 (例如：`[DocType]_[VersionTag]_[OriginalName]` 或 `[DocType]_[VersionTag]_[Timestamp]`)。
      - 设计第一版简单的文件夹存放规则 (例如：`CLARITY_FILE_ROOT/Projects/[ProjectName]/[LogicalDocName]/[GeneratedFileName]`)。
  3.  **[设计与编码]** 文档版本添加：
      - 用户为逻辑文档添加新版本，输入 `version_tag`。
      - 应用根据V1规则生成文件名和路径，将文件存入，并在 `managed_files` 和 `document_versions` 中记录。
  4.  **[设计与编码]** 版本列表展示：能看到一个逻辑文档下的所有版本。
  5.  **[设计与编码]** 打开文件/文件夹功能。
- **产出：** 用户可以按“文档系列”来管理文件，每个系列下有不同版本。文件开始有了初步的规范命名和结构化存放。
- **价值：** 解决了“一个PPT有哪些版本”的基础问题，文件存放开始有序。

### Phase 2: 引入比赛维度与更完善的智能命名 (Schema 扩展)

- **目标：** 将比赛信息纳入管理，并让文档版本能与比赛关联，智能命名规则更完善。
- **激活的Schema部分：**
  - `competition_series`: 全部字段。
  - `competition_milestones`: 全部字段 (除 `notification_managed_file_id` 初期可不做)。
  - `document_versions`: 补充 `competition_milestone_id`, `competition_project_name`, `is_generic_version`。
  - `logical_documents`: 补充 `status`, `current_official_version_id`。
- **核心功能实现：**
  1.  **[设计与编码]** 赛事系列和里程碑的CRUD。
  2.  **[设计与编码]** **智能命名与存放 V2：**
      - 文件名和路径规则中加入比赛、赛段、是否通用、参赛项目名等因素。
  3.  **[设计与编码]** 文档版本添加/编辑时，允许关联到比赛里程碑，填写参赛项目名。
  4.  **[设计与编码]** 逻辑文档状态管理和官方版本指定功能。
  5.  **[设计与编码]** 视图调整：能按比赛筛选或查看与比赛相关的文档。
- **产出：** 用户可以管理比赛信息，并将文档版本与具体比赛的特定阶段关联起来，文件命名和存放更加精细和有针对性。
- **价值：** 解决了“针对不同比赛制作不同版本资料”的核心痛点。

### Phase 3: 引入资产、共享资源与标签 (Schema 丰富)

- **目标：** 管理项目资产和团队共享资源，引入标签系统增强分类和查找。
- **激活的Schema部分：**
  - `project_assets`: 全部字段。
  - `shared_resources`: 全部字段。
  - `tags`: 全部字段。
  - 所有相关的中间表：`project_asset_tags`, `shared_resource_tags`, `project_tags`, `document_version_tags`, `project_shared_resources`。
  - `projects`: 补充 `current_cover_asset_id`。
- **核心功能实现：**
  1.  **[设计与编码]** 项目资产的CRUD，并能将其设置为项目封面。
  2.  **[设计与编码]** 共享资源的CRUD，并能将其关联到项目。
  3.  **[设计与编码]** 标签的创建和管理。
  4.  **[设计与编码]** 在添加/编辑文档版本、项目资产、共享资源、项目时，允许打标签。
  5.  **[设计与编码]** 基于标签的筛选和搜索功能。
- **产出：** 软件能管理更多类型的文件，分类和查找能力得到极大增强。
- **价值：** 解决了“核心成果、专利、截图等如何归类和查找”的问题。

### Phase 4: 引入经费与流程管理 (Schema 完整激活)

- **目标：** 实现经费报销追踪，并完善项目与比赛的关联。
- **激活的Schema部分：**
  - `expense_trackings`: 全部字段。
  - `project_competition_milestones`: 全部字段。
  - (可能) `competition_milestones`: 激活 `notification_managed_file_id` 功能。
  - (可能) `expense_trackings`: 激活 `invoice_managed_file_id` 功能。
- **核心功能实现：**
  1.  **[设计与编码]** 经费报销的CRUD，看板视图及状态管理。
  2.  **[设计与编码]** (可选) 上传和管理发票文件、比赛通知文件。
  3.  **[设计与编码]** 项目与赛事里程碑的直接关联管理 (`project_competition_milestones`)。
  4.  **[设计与编码]** 在项目视图或比赛视图中展示这些关联信息。
- **产出：** 软件功能趋于完整，覆盖了你最初描述的几乎所有痛点。
- **价值：** 解决了经费追踪和项目参赛情况宏观管理的问题。

### 后续阶段：优化、高级功能与打磨

- **[设计与编码]** 全局搜索的增强。
- **[设计与编码]** UI/UX 的持续优化。
- **[设计与编码]** 性能优化。
- **[设计与编码]** (可选) 文件元数据修改导致的文件重命名/移动。
- **[设计与编码]** (可选) 用户自定义配置项。
- **[设计与编码]** (可选) 数据备份与恢复。

## 项目独特性与价值

- **深度定制：** 完美契合学术团队在多项目、多比赛场景下的特定需求，解决的是真实存在的、具体的痛点。
- **自动化与规范化：** 通过强制智能命名和结构化存放，从根本上解决文件混乱问题，提升管理效率。
- **信息聚合与关联：** 将原本分散的各类信息整合到统一平台，并通过关联关系赋予信息更强的上下文和可发现性。
- **本地化与可控性：** 满足对数据隐私和自主可控有较高要求的用户。
- **效率提升：** 最终目标是显著提升信息查找、版本追溯、事务管理的效率，让团队能更专注于核心项目工作。

## 潜在挑战与未来展望

- **通用性：** 作为高度定制化的解决方案，直接推广到其他不同领域的通用性有限，但其核心问题解决思路对类似场景有借鉴意义。
- **开发投入：** 作为一个功能相对全面的自研项目，需要持续的时间和精力投入。
- **用户习惯的改变：** 强制性的管理方式需要用户适应新的工作流程。
- **未来可扩展方向：** 可配置性增强、模块化、与其他工具的集成、更智能的分析与推荐等。

总结来说，ClarityFile / 明档 是一个目标明确、设计精良、致力于通过智能化和规范化手段解决特定领域信息管理难题的桌面应用程序项目。它以解决你和你团队的实际痛点为出发点，通过精心设计的数据库和文件系统映射策略，有望成为一个高效、直观、可靠的“第二大脑”和工作枢纽。
