{"title": "Competition Center", "series": "Competition Series", "timeline": "Timeline", "milestones": {"registration": "Registration Opens", "registrationEnd": "Registration Closes", "submission": "Submission Opens", "submissionEnd": "Submission Closes", "preliminaryReview": "Preliminary Review", "finalReview": "Final Review", "announcement": "Results Announcement", "award": "Award Ceremony"}, "create": "Create Competition", "edit": "Edit Competition", "delete": "Delete Competition", "join": "Join Competition", "leave": "Leave Competition", "search": "Search Competitions", "filter": "Filter Competitions", "upcoming": "Upcoming", "ongoing": "Ongoing", "completed": "Completed", "cancelled": "Cancelled", "types": {"innovation": "Innovation & Entrepreneurship", "academic": "Academic Competition", "technology": "Technology Competition", "design": "Design Competition", "business": "Business Competition", "other": "Other"}, "status": {"draft": "Draft", "published": "Published", "registration": "Registration Open", "ongoing": "Ongoing", "judging": "Under Review", "completed": "Completed", "cancelled": "Cancelled"}, "fields": {"name": "Competition Name", "description": "Competition Description", "type": "Competition Type", "category": "Competition Category", "level": "Competition Level", "organizer": "Organizer", "sponsor": "Sponsor", "venue": "Venue", "startDate": "Start Date", "endDate": "End Date", "registrationDeadline": "Registration Deadline", "submissionDeadline": "Submission Deadline", "maxParticipants": "Max Participants", "currentParticipants": "Current Participants", "prize": "Prize", "requirements": "Requirements", "rules": "Rules", "contact": "Contact", "website": "Website", "tags": "Tags"}, "dueDate": "", "dueDateLabel": "", "createTime": "", "overdue": "", "dueToday": "", "daysLeft": "", "noDescription": "", "noDueDate": ""}