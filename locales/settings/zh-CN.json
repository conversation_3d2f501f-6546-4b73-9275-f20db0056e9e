{"title": "设置", "categories": {"general": "常规设置", "appearance": "外观设置", "notifications": "通知设置", "language": "语言与地区", "accessibility": "无障碍", "audioVideo": "音频与视频", "privacy": "隐私与可见性", "features": "功能设置", "advanced": "高级设置"}, "descriptions": {"general": "管理应用程序的常规设置", "appearance": "管理应用程序的外观设置", "notifications": "管理应用程序的通知设置", "language": "管理应用程序的语言与地区", "accessibility": "管理应用程序的无障碍", "audioVideo": "管理应用程序的音频与视频", "privacy": "管理应用程序的隐私与可见性", "features": "管理应用程序的功能特性和工具", "advanced": "管理应用程序的高级设置"}, "general": {"title": "常规设置", "appName": "应用程序名称", "appNameDescription": "自定义应用程序显示名称", "appNameValidation": "应用名称不能为空", "autoSave": "自动保存", "autoSaveDescription": "自动保存文档和项目更改", "autoBackup": "自动备份", "autoBackupDescription": "定期自动备份项目数据", "backupInterval": "备份间隔", "backupIntervalDescription": "自动备份的时间间隔（分钟）", "defaultProjectPath": "默认项目路径", "defaultProjectPathDescription": "新建项目的默认保存位置", "maxRecentProjects": "最近项目数量", "maxRecentProjectsDescription": "显示的最近项目数量上限", "description": "描述", "descriptionDescription": "应用程序描述信息", "autoStart": "开机自启动", "autoStartDescription": "系统启动时自动启动应用程序", "minimizeToTray": "最小化到系统托盘", "minimizeToTrayDescription": "关闭窗口时最小化到系统托盘而不是退出", "closeToTray": "关闭到系统托盘", "closeToTrayDescription": "点击关闭按钮时最小化到系统托盘", "selectFolder": "选择文件夹", "backupSettings": "备份设置", "backupLocation": "备份位置", "maxBackups": "最大备份数量"}, "appearance": {"title": "外观设置", "theme": "主题模式", "themeDescription": "选择应用程序的基础外观主题", "fontFamily": "字体系列", "fontFamilyDescription": "选择界面使用的字体", "basicThemeSettings": "基础主题设置", "basicThemeDescription": "选择应用程序的基础外观主题。注意：自定义主题激活时会覆盖这些设置。", "themes": {"light": "浅色", "lightDescription": "明亮的界面主题", "dark": "深色", "darkDescription": "深色的界面主题", "system": "跟随系统", "systemDescription": "根据系统设置自动切换"}, "fonts": {"system": "系统默认", "inter": "Inter", "roboto": "Roboto", "notoSans": "Noto Sans", "sourceHanSans": "思源黑体"}, "selectFont": "选择字体", "customTheme": {"title": "自定义主题", "sectionDescription": "导入和管理您的自定义主题", "currentStatus": "当前使用自定义主题 ({{count}} 个可用)", "availableThemes": "{{count}} 个自定义主题可用", "overrideNotice": "自定义主题会覆盖上方的基础主题设置", "switchToDefault": "切回默认", "importTheme": "导入主题", "collapse": "收起", "themeName": "主题名称", "themeNamePlaceholder": "例如：Ocean Blue", "themeDescription": "描述 (可选)", "descriptionPlaceholder": "主题描述", "cssContent": "CSS 内容", "cssContentPlaceholder": "粘贴您的主题 CSS 代码...", "fontDetection": "智能字体检测显示", "colorPreview": "色彩预览", "importing": "导入中...", "preview": "预览", "clearPreview": "清除预览", "loadTemplate": "加载模板", "savedThemes": "已保存的主题", "applied": "已应用", "themeColors": "主题色彩", "applyTheme": "应用主题", "editTheme": "编辑主题", "exportTheme": "导出主题", "deleteTheme": "删除主题", "saving": "保存中...", "save": "保存", "cancel": "取消", "created": "创建", "updated": "更新", "createdAt": "{{date}}创建", "updatedAt": "{{date}}更新", "errors": {"nameAndCssRequired": "请输入主题名称和 CSS 内容", "validationFailed": "主题验证失败: {{errors}}", "nameExists": "主题名称已存在，请使用不同的名称", "importFailed": "导入失败：{{error}}", "cssRequired": "请输入 CSS 内容进行预览", "previewFailed": "预览失败：{{error}}", "applyFailed": "应用失败：{{error}}", "deleteFailed": "删除失败：{{error}}", "themeNotFound": "主题不存在", "exportFailed": "导出失败：{{error}}", "switchFailed": "切换失败：{{error}}", "completeInfo": "请填写完整的主题信息", "updateFailed": "更新失败：{{error}}"}}}, "language": {"title": "语言与地区", "currentLanguage": "当前语言", "selectLanguage": "选择语言", "languageChanged": "语言已更改", "languageChangedDescription": "语言设置已更新，部分更改可能需要重启应用程序才能生效", "restartRequired": "需要重启", "restartNow": "立即重启", "restartLater": "稍后重启", "supportedLanguages": {"zh-CN": "简体中文", "en-US": "English (US)"}, "dateFormat": "日期格式", "timeFormat": "时间格式", "timezone": "时区", "currency": "货币", "numberFormat": "数字格式", "autoDetectLanguage": "自动检测语言", "autoDetectLanguageDescription": "根据系统语言自动切换应用语言", "regionFormat": "地区格式", "regionFormatDescription": "配置日期、时间和数字的显示格式", "selectDateFormat": "选择日期格式", "selectTimeFormat": "选择时间格式"}, "notifications": {"title": "通知设置", "basicSettings": "基本设置", "basicDescription": "管理应用程序的通知偏好", "enabled": "启用通知", "enabledDescription": "接收应用程序通知", "sound": "通知声音", "soundDescription": "播放通知声音", "desktop": "桌面通知", "desktopDescription": "显示桌面通知弹窗", "projectUpdates": "项目更新", "projectUpdatesDescription": "项目状态变更时通知", "fileChanges": "文件变更", "fileChangesDescription": "文件修改时通知", "systemAlerts": "系统警告", "systemAlertsDescription": "系统错误和警告通知", "soundType": "声音类型", "soundTypeDescription": "选择通知声音", "soundOptions": {"default": "默认", "chime": "铃声", "bell": "钟声", "none": "无声音"}, "advancedSettings": "高级设置", "advancedDescription": "配置详细的通知规则和时间"}, "accessibility": {"title": "无障碍功能", "description": "配置应用程序的无障碍功能", "inDevelopment": "无障碍设置功能正在开发中...", "upcomingFeatures": "即将支持屏幕阅读器、高对比度模式、键盘导航等功能"}, "privacy": {"title": "隐私与可见性", "dataPrivacy": "数据隐私", "dataPrivacyDescription": "管理应用程序的隐私设置和数据保护", "visibilitySettings": "可见性设置", "visibilityDescription": "控制信息的可见性和共享范围", "privacyInDevelopment": "隐私设置功能正在开发中...", "privacyUpcomingFeatures": "即将支持数据加密、访问权限、隐私模式等功能", "visibilityInDevelopment": "可见性设置功能正在开发中...", "visibilityUpcomingFeatures": "即将支持项目可见性、文件共享权限、协作设置等功能"}, "advanced": {"title": "高级设置", "developerOptions": "开发者选项", "developerDescription": "配置应用程序的高级功能和开发者选项", "debugMode": "调试模式", "debugModeDescription": "启用详细的调试信息", "developerTools": "开发者工具", "developerToolsDescription": "启用开发者工具访问", "experimentalFeatures": "实验性功能", "experimentalFeaturesDescription": "启用正在开发中的实验性功能（可能不稳定）", "performanceSettings": "性能设置", "performanceDescription": "优化应用程序性能和资源使用", "enableHardwareAcceleration": "硬件加速", "hardwareAccelerationDescription": "使用GPU加速渲染（重启后生效）", "maxMemoryUsage": "最大内存使用", "maxMemoryUsageDescription": "限制应用程序的内存使用量", "cacheSize": "缓存大小", "cacheSizeDescription": "设置应用程序缓存大小限制", "resetSection": "重置设置", "resetDescription": "将所有设置恢复为默认值", "resetButton": "重置所有设置", "resetConfirmTitle": "确认重置设置", "resetConfirmDescription": "此操作将删除所有自定义设置并恢复为默认值。此操作无法撤销。", "resetSuccessMessage": "所有设置已重置为默认值", "logLevel": "日志级别", "logLevelDescription": "设置应用程序日志记录级别", "maxLogFiles": "最大日志文件数", "maxLogFilesDescription": "保留的日志文件数量上限", "enableTelemetry": "启用遥测", "enableTelemetryDescription": "发送匿名使用数据以改进产品"}, "features": {"title": "功能设置", "commandPalette": {"title": "命令面板", "description": "查看和管理命令面板插件", "shortcut": "快捷键: ⌘K 或 Ctrl+K", "pluginManagement": {"title": "插件管理", "description": "查看当前注册的命令面板插件和它们发布的命令", "stats": "已注册 {{pluginCount}} 个插件，发布了 {{commandCount}} 个命令", "noPlugins": "暂无注册的插件", "commandsPublished": "发布了 {{count}} 个命令", "enabled": "已启用", "disabled": "已禁用", "toggleError": "插件状态切换失败", "toggleErrorDescription": "无法更新插件状态，请稍后重试"}, "commandTypes": {"action": "Action", "render": "Render"}}}, "audioVideo": {"title": "音频与视频", "audioSettings": "音频设置", "audioDescription": "配置音频输入输出设备和音质选项", "videoSettings": "视频设置", "videoDescription": "配置视频录制和播放选项", "inDevelopment": "音频与视频设置功能正在开发中...", "upcomingFeatures": "即将支持音频设备选择、音质调节、视频录制等功能"}, "fontDetection": {"detectedFonts": "检测到的字体", "googleFonts": "Google Fonts", "systemFonts": "系统字体", "customFonts": "自定义字体"}}