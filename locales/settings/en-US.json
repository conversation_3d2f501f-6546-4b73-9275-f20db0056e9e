{"title": "Settings", "categories": {"general": "General", "appearance": "Appearance", "notifications": "Notifications", "language": "Language & Region", "accessibility": "Accessibility", "audioVideo": "Audio & Video", "privacy": "Privacy & Visibility", "features": "Features", "advanced": "Advanced"}, "descriptions": {"general": "Manage general application settings", "appearance": "Manage application appearance settings", "notifications": "Manage application notification settings", "language": "Manage application language & region", "accessibility": "Manage application accessibility", "audioVideo": "Manage application audio & video", "privacy": "Manage application privacy & visibility", "features": "Manage application features and tools", "advanced": "Manage application advanced settings"}, "general": {"title": "General Settings", "appName": "Application Name", "appNameDescription": "Customize the application display name", "appNameValidation": "Application name cannot be empty", "autoSave": "Auto save", "autoSaveDescription": "Automatically save document and project changes", "autoBackup": "Auto backup", "autoBackupDescription": "Automatically backup project data periodically", "backupInterval": "Backup interval", "backupIntervalDescription": "Time interval for automatic backup (minutes)", "defaultProjectPath": "Default project path", "defaultProjectPathDescription": "Default save location for new projects", "maxRecentProjects": "Max recent projects", "maxRecentProjectsDescription": "Maximum number of recent projects to display", "description": "Description", "descriptionDescription": "Application description information", "autoStart": "Start at login", "autoStartDescription": "Automatically start the application when the system starts", "minimizeToTray": "Minimize to system tray", "minimizeToTrayDescription": "Minimize to system tray instead of closing when window is closed", "closeToTray": "Close to system tray", "closeToTrayDescription": "Minimize to system tray when close button is clicked", "selectFolder": "Select Folder", "backupSettings": "Backup Settings", "backupLocation": "Backup location", "maxBackups": "Maximum backups"}, "appearance": {"title": "Appearance Settings", "theme": "Theme Mode", "themeDescription": "Choose the basic appearance theme for the application", "fontFamily": "Font Family", "fontFamilyDescription": "Select the font used in the interface", "basicThemeSettings": "Basic Theme Settings", "basicThemeDescription": "Choose the basic appearance theme for the application. Note: Custom themes will override these settings when activated.", "themes": {"light": "Light", "lightDescription": "Bright theme", "dark": "Dark", "darkDescription": "Dark theme", "system": "Follow System", "systemDescription": "Automatically switch based on system settings"}, "fonts": {"system": "System Default", "inter": "Inter", "roboto": "Roboto", "notoSans": "Noto Sans", "sourceHanSans": "Source <PERSON>"}, "selectFont": "Select Font", "customTheme": {"title": "Custom Themes", "sectionDescription": "Import and manage your custom themes", "currentStatus": "Currently using custom theme ({{count}} available)", "availableThemes": "{{count}} custom themes available", "overrideNotice": "Custom themes will override the basic theme settings above", "switchToDefault": "Switch to Default", "importTheme": "Import Theme", "collapse": "Collapse", "themeName": "Theme Name", "themeNamePlaceholder": "e.g., Ocean Blue", "themeDescription": "Description (Optional)", "descriptionPlaceholder": "Theme description", "cssContent": "CSS Content", "cssContentPlaceholder": "Paste your theme CSS code...", "fontDetection": "Smart Font Detection", "colorPreview": "Color Preview", "importing": "Importing...", "preview": "Preview", "clearPreview": "Clear Preview", "loadTemplate": "Load Template", "savedThemes": "Saved Themes", "applied": "Applied", "themeColors": "Theme Colors", "applyTheme": "Apply Theme", "editTheme": "Edit Theme", "exportTheme": "Export Theme", "deleteTheme": "Delete Theme", "saving": "Saving...", "save": "Save", "cancel": "Cancel", "created": "Created", "updated": "Updated", "createdAt": "Created {{date}}", "updatedAt": "Updated {{date}}", "errors": {"nameAndCssRequired": "Please enter theme name and CSS content", "validationFailed": "Theme validation failed: {{errors}}", "nameExists": "Theme name already exists, please use a different name", "importFailed": "Import failed: {{error}}", "cssRequired": "Please enter CSS content for preview", "previewFailed": "Preview failed: {{error}}", "applyFailed": "Apply failed: {{error}}", "deleteFailed": "Delete failed: {{error}}", "themeNotFound": "Theme not found", "exportFailed": "Export failed: {{error}}", "switchFailed": "Switch failed: {{error}}", "completeInfo": "Please fill in complete theme information", "updateFailed": "Update failed: {{error}}"}}}, "language": {"title": "Language & Region", "currentLanguage": "Current Language", "selectLanguage": "Select Language", "languageChanged": "Language Changed", "languageChangedDescription": "Language settings have been updated. Some changes may require restarting the application to take effect", "restartRequired": "<PERSON><PERSON> Required", "restartNow": "Restart Now", "restartLater": "<PERSON><PERSON>", "supportedLanguages": {"zh-CN": "简体中文", "en-US": "English (US)"}, "dateFormat": "Date Format", "timeFormat": "Time Format", "timezone": "Timezone", "currency": "<PERSON><PERSON><PERSON><PERSON>", "numberFormat": "Number Format", "autoDetectLanguage": "Auto detect language", "autoDetectLanguageDescription": "Automatically switch app language based on system language", "regionFormat": "Region Format", "regionFormatDescription": "Configure date, time and number display formats", "selectDateFormat": "Select date format", "selectTimeFormat": "Select time format"}, "notifications": {"title": "Notification Settings", "basicSettings": "Basic Settings", "basicDescription": "Manage application notification preferences", "enabled": "Enable notifications", "enabledDescription": "Receive application notifications", "sound": "Notification sound", "soundDescription": "Play notification sounds", "desktop": "Desktop notifications", "desktopDescription": "Show desktop notification popups", "projectUpdates": "Project updates", "projectUpdatesDescription": "Notify when project status changes", "fileChanges": "File changes", "fileChangesDescription": "Notify when files are modified", "systemAlerts": "System alerts", "systemAlertsDescription": "System error and warning notifications", "soundType": "Sound type", "soundTypeDescription": "Choose notification sound", "soundOptions": {"default": "<PERSON><PERSON><PERSON>", "chime": "<PERSON><PERSON>", "bell": "Bell", "none": "No sound"}, "advancedSettings": "Advanced Settings", "advancedDescription": "Configure detailed notification rules and timing"}, "accessibility": {"title": "Accessibility Features", "description": "Configure application accessibility features", "inDevelopment": "Accessibility settings are under development...", "upcomingFeatures": "Coming soon: screen reader support, high contrast mode, keyboard navigation, and more"}, "privacy": {"title": "Privacy & Visibility", "dataPrivacy": "Data Privacy", "dataPrivacyDescription": "Manage application privacy settings and data protection", "visibilitySettings": "Visibility Settings", "visibilityDescription": "Control information visibility and sharing scope", "privacyInDevelopment": "Privacy settings are under development...", "privacyUpcomingFeatures": "Coming soon: data encryption, access permissions, privacy mode, and more", "visibilityInDevelopment": "Visibility settings are under development...", "visibilityUpcomingFeatures": "Coming soon: project visibility, file sharing permissions, collaboration settings, and more"}, "advanced": {"title": "Advanced Settings", "developerOptions": "Developer Options", "developerDescription": "Configure advanced features and developer options", "debugMode": "Debug mode", "debugModeDescription": "Enable detailed debug information", "developerTools": "Developer tools", "developerToolsDescription": "Enable developer tools access", "experimentalFeatures": "Experimental features", "experimentalFeaturesDescription": "Enable experimental features under development (may be unstable)", "performanceSettings": "Performance Settings", "performanceDescription": "Optimize application performance and resource usage", "enableHardwareAcceleration": "Hardware acceleration", "hardwareAccelerationDescription": "Use GPU acceleration for rendering (requires restart)", "maxMemoryUsage": "Maximum memory usage", "maxMemoryUsageDescription": "Limit application memory usage", "cacheSize": "Cache size", "cacheSizeDescription": "Set application cache size limit", "resetSection": "Reset Settings", "resetDescription": "Restore all settings to default values", "resetButton": "Reset all settings", "resetConfirmTitle": "Confirm reset settings", "resetConfirmDescription": "This will delete all custom settings and restore defaults. This action cannot be undone.", "resetSuccessMessage": "All settings have been reset to default values", "logLevel": "Log level", "logLevelDescription": "Set application logging level", "maxLogFiles": "Maximum log files", "maxLogFilesDescription": "Maximum number of log files to keep", "enableTelemetry": "Enable telemetry", "enableTelemetryDescription": "Send anonymous usage data to improve the product"}, "features": {"title": "Features", "commandPalette": {"title": "Command Palette", "description": "View and manage command palette plugins", "shortcut": "Shortcut: ⌘K or Ctrl+K", "pluginManagement": {"title": "Plugin Management", "description": "View currently registered command palette plugins and their published commands", "stats": "{{pluginCount}} plugins registered, {{commandCount}} commands published", "noPlugins": "No plugins registered", "commandsPublished": "{{count}} commands published", "enabled": "Enabled", "disabled": "Disabled", "enableSuccess": "Plugin enabled", "disableSuccess": "Plugin disabled", "toggleDescription": "Plugin {{pluginName}} status updated", "toggleError": "Failed to toggle plugin", "toggleErrorDescription": "Unable to update plugin status, please try again later"}, "commandTypes": {"action": "Action", "render": "Render"}}}, "audioVideo": {"title": "Audio & Video", "audioSettings": "Audio Settings", "audioDescription": "Configure audio input/output devices and quality options", "videoSettings": "Video Settings", "videoDescription": "Configure video recording and playback options", "inDevelopment": "Audio & video settings are under development...", "upcomingFeatures": "Coming soon: audio device selection, quality adjustment, video recording, and more"}, "fontDetection": {"detectedFonts": "Detected Fonts", "googleFonts": "Google Fonts", "systemFonts": "System Fonts", "customFonts": "Custom Fonts"}}